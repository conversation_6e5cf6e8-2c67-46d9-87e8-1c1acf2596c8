# LiveStream AI Chat Bot

A desktop application that monitors and analyzes livestream content in real-time to automatically generate contextually appropriate chat responses.

## Features

- **Real-time Content Analysis**: Captures and analyzes livestream audio/video content
- **AI-Powered Response Generation**: Uses advanced AI models to understand context and generate natural responses
- **Multi-Platform Support**: Primary support for Twitch, with YouTube Live integration planned
- **Smart Safety Filters**: Built-in safeguards to prevent inappropriate responses
- **Customizable Behavior**: User-configurable response style, frequency, and topics
- **Manual Override**: Full user control with pause and manual intervention capabilities

## Architecture

### Backend (Python)
- **Screen Capture**: Real-time screen and audio capture
- **AI Processing**: Speech recognition, computer vision, sentiment analysis
- **Platform APIs**: Twitch/YouTube chat integration
- **Response Engine**: Context-aware message generation

### Frontend (Electron + React)
- **Configuration UI**: Settings for platforms, AI models, response behavior
- **Monitoring Dashboard**: Real-time stream analysis and chat activity
- **Manual Controls**: Override, pause, and manual message sending
- **Analytics**: Response performance and engagement metrics

## Technology Stack

- **Backend**: Python 3.11+
  - OpenAI Whisper (speech recognition)
  - OpenCV (computer vision)
  - OpenAI GPT (response generation)
  - TwitchIO (Twitch API)
  - PyQt6 (system integration)

- **Frontend**: Electron + React + TypeScript
  - Material-UI (component library)
  - Socket.IO (real-time communication)
  - Chart.js (analytics visualization)

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd livestream-ai-chatbot

# Install Python dependencies
cd backend
pip install -r requirements.txt

# Install Node.js dependencies
cd ../frontend
npm install

# Build and run
npm run electron:dev
```

## Configuration

1. **Platform Setup**: Configure Twitch/YouTube API credentials
2. **AI Models**: Set up OpenAI API keys and model preferences
3. **Response Behavior**: Customize response style, frequency, and content filters
4. **Safety Settings**: Configure content moderation and response safeguards

## Usage

1. Launch the application
2. Configure your target livestream platform and credentials
3. Select the stream window or browser tab to monitor
4. Start the AI analysis and response generation
5. Monitor real-time activity and manually override when needed

## Safety and Compliance

- **Platform TOS Compliance**: Designed to respect platform terms of service
- **Rate Limiting**: Built-in chat rate limiting to avoid spam detection
- **Content Filtering**: Multiple layers of content moderation
- **Human Oversight**: Always allows manual intervention and control

## Development Status

This project is in active development. Current focus areas:
- Core screen capture and audio processing
- AI model integration and optimization
- Platform API integration and testing
- User interface development and refinement

## Contributing

Please read our contributing guidelines and code of conduct before submitting pull requests.

## License

[License information to be added]

## Disclaimer

This tool is designed for educational and entertainment purposes. Users are responsible for ensuring compliance with platform terms of service and applicable laws. The developers are not responsible for any misuse of this software.