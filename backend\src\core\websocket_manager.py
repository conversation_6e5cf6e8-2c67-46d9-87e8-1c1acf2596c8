"""
WebSocket connection manager for real-time communication with the frontend.

This module handles WebSocket connections, message broadcasting,
and real-time updates between the backend and frontend.
"""

import json
import asyncio
from typing import List, Dict, Any
from fastapi import WebSocket
from loguru import logger


class WebSocketManager:
    """Manages WebSocket connections and message broadcasting."""

    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_data: Dict[WebSocket, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_data[websocket] = {
            "connected_at": asyncio.get_event_loop().time(),
            "client_id": id(websocket)
        }
        logger.info(f"WebSocket client connected: {id(websocket)}")

        # Send welcome message
        await self.send_personal_message({
            "type": "connection_established",
            "client_id": id(websocket),
            "message": "Connected to LiveStream AI Chat Bot"
        }, websocket)

    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            client_id = self.connection_data.get(websocket, {}).get("client_id", "unknown")
            if websocket in self.connection_data:
                del self.connection_data[websocket]
            logger.info(f"WebSocket client disconnected: {client_id}")

    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected clients."""
        if not self.active_connections:
            return

        message_text = json.dumps(message)
        disconnected = []

        for connection in self.active_connections:
            try:
                await connection.send_text(message_text)
            except Exception as e:
                logger.error(f"Error broadcasting to client {id(connection)}: {e}")
                disconnected.append(connection)

        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

    async def send_status_update(self, status: str, data: Dict[str, Any] = None):
        """Send a status update to all connected clients."""
        message = {
            "type": "status_update",
            "status": status,
            "timestamp": asyncio.get_event_loop().time(),
            "data": data or {}
        }
        await self.broadcast(message)

    async def send_analysis_result(self, analysis_type: str, result: Dict[str, Any]):
        """Send analysis results to all connected clients."""
        message = {
            "type": "analysis_result",
            "analysis_type": analysis_type,
            "result": result,
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast(message)

    async def send_chat_response(self, platform: str, channel: str, message: str, metadata: Dict[str, Any] = None):
        """Send chat response information to all connected clients."""
        response_data = {
            "type": "chat_response",
            "platform": platform,
            "channel": channel,
            "message": message,
            "metadata": metadata or {},
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast(response_data)

    async def send_error(self, error_type: str, error_message: str, details: Dict[str, Any] = None):
        """Send error information to all connected clients."""
        error_data = {
            "type": "error",
            "error_type": error_type,
            "message": error_message,
            "details": details or {},
            "timestamp": asyncio.get_event_loop().time()
        }
        await self.broadcast(error_data)

    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.active_connections)

    def get_connection_info(self) -> List[Dict[str, Any]]:
        """Get information about all active connections."""
        return [
            {
                "client_id": data.get("client_id"),
                "connected_at": data.get("connected_at"),
                "connection_time": asyncio.get_event_loop().time() - data.get("connected_at", 0)
            }
            for data in self.connection_data.values()
        ]