# LiveStream AI Chat Bot Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
HOST=localhost
PORT=8000
DEBUG=true

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Twitch Configuration
TWITCH_CLIENT_ID=your_twitch_client_id_here
TWITCH_CLIENT_SECRET=your_twitch_client_secret_here
TWITCH_OAUTH_TOKEN=your_twitch_oauth_token_here

# YouTube Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# AI Model Configuration
AI_MODEL=gpt-3.5-turbo
MAX_TOKENS=150
TEMPERATURE=0.7

# Platform Settings
PRIMARY_PLATFORM=twitch
TARGET_CHANNEL=your_target_channel_here

# Response Behavior
RESPONSE_FREQUENCY=0.3
MIN_RESPONSE_INTERVAL=30
MAX_RESPONSE_LENGTH=200

# Content Analysis Features
ENABLE_SPEECH_RECOGNITION=true
ENABLE_COMPUTER_VISION=true
ENABLE_SENTIMENT_ANALYSIS=true

# Safety Settings
ENABLE_CONTENT_FILTER=true
SAFE_MODE=true
BANNED_WORDS=

# Screen Capture Settings
CAPTURE_FPS=2
CAPTURE_QUALITY=50
AUDIO_SAMPLE_RATE=16000