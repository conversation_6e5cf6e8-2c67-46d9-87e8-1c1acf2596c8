"""
Main application class for the LiveStream AI Chat Bot.

This module coordinates all the components including screen capture,
AI analysis, platform integration, and response generation.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from loguru import logger

from .config import Settings
from .websocket_manager import WebSocketManager


class LiveStreamChatBot:
    """Main application class that coordinates all components."""

    def __init__(self, settings: Settings, websocket_manager: WebSocketManager):
        self.settings = settings
        self.websocket_manager = websocket_manager

        # Component instances (will be initialized in initialize())
        self.screen_capture = None
        self.audio_processor = None
        self.ai_analyzer = None
        self.platform_manager = None
        self.response_generator = None

        # Application state
        self.is_running = False
        self.is_analyzing = False
        self.last_response_time = 0
        self.analysis_tasks = []

        logger.info("LiveStreamChatBot instance created")

    async def initialize(self):
        """Initialize all components and start the application."""
        try:
            logger.info("Initializing LiveStream AI Chat Bot components...")

            # Initialize components (placeholder imports for now)
            await self._initialize_screen_capture()
            await self._initialize_audio_processor()
            await self._initialize_ai_analyzer()
            await self._initialize_platform_manager()
            await self._initialize_response_generator()

            self.is_running = True
            logger.info("All components initialized successfully")

            # Send status update to frontend
            await self.websocket_manager.send_status_update(
                "initialized",
                {"message": "All components ready"}
            )

        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            await self.websocket_manager.send_error(
                "initialization_error",
                str(e),
                {"component": "application"}
            )
            raise

    async def shutdown(self):
        """Shutdown all components and cleanup resources."""
        logger.info("Shutting down LiveStream AI Chat Bot...")

        self.is_running = False
        self.is_analyzing = False

        # Cancel all running analysis tasks
        for task in self.analysis_tasks:
            if not task.done():
                task.cancel()

        # Shutdown components
        if self.screen_capture:
            await self.screen_capture.shutdown()
        if self.audio_processor:
            await self.audio_processor.shutdown()
        if self.ai_analyzer:
            await self.ai_analyzer.shutdown()
        if self.platform_manager:
            await self.platform_manager.shutdown()
        if self.response_generator:
            await self.response_generator.shutdown()

        logger.info("Shutdown complete")

    async def handle_websocket_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket messages from the frontend."""
        message_type = data.get("type")

        if message_type == "start_analysis":
            await self._start_analysis(data)
        elif message_type == "stop_analysis":
            await self._stop_analysis()
        elif message_type == "update_settings":
            await self._update_settings(data.get("settings", {}))
        elif message_type == "manual_response":
            await self._send_manual_response(data)
        elif message_type == "get_status":
            await self._send_status()
        else:
            logger.warning(f"Unknown message type: {message_type}")

    async def get_health_status(self) -> Dict[str, Any]:
        """Get the health status of all components."""
        return {
            "application": "healthy" if self.is_running else "stopped",
            "screen_capture": "healthy" if self.screen_capture else "not_initialized",
            "audio_processor": "healthy" if self.audio_processor else "not_initialized",
            "ai_analyzer": "healthy" if self.ai_analyzer else "not_initialized",
            "platform_manager": "healthy" if self.platform_manager else "not_initialized",
            "response_generator": "healthy" if self.response_generator else "not_initialized",
            "websocket_connections": self.websocket_manager.get_connection_count()
        }

    # Placeholder initialization methods (will be implemented in later tasks)
    async def _initialize_screen_capture(self):
        """Initialize screen capture component."""
        logger.info("Screen capture component initialized (placeholder)")

    async def _initialize_audio_processor(self):
        """Initialize audio processing component."""
        logger.info("Audio processor component initialized (placeholder)")

    async def _initialize_ai_analyzer(self):
        """Initialize AI analysis component."""
        logger.info("AI analyzer component initialized (placeholder)")

    async def _initialize_platform_manager(self):
        """Initialize platform integration component."""
        logger.info("Platform manager component initialized (placeholder)")

    async def _initialize_response_generator(self):
        """Initialize response generation component."""
        logger.info("Response generator component initialized (placeholder)")

    # Placeholder message handlers (will be implemented in later tasks)
    async def _start_analysis(self, data: Dict[str, Any]):
        """Start content analysis."""
        logger.info("Starting content analysis (placeholder)")
        self.is_analyzing = True
        await self.websocket_manager.send_status_update("analysis_started")

    async def _stop_analysis(self):
        """Stop content analysis."""
        logger.info("Stopping content analysis (placeholder)")
        self.is_analyzing = False
        await self.websocket_manager.send_status_update("analysis_stopped")

    async def _update_settings(self, settings: Dict[str, Any]):
        """Update application settings."""
        logger.info(f"Updating settings: {settings}")
        await self.websocket_manager.send_status_update("settings_updated", settings)

    async def _send_manual_response(self, data: Dict[str, Any]):
        """Send a manual chat response."""
        message = data.get("message", "")
        platform = data.get("platform", self.settings.primary_platform)
        logger.info(f"Sending manual response to {platform}: {message}")

    async def _send_status(self):
        """Send current status to frontend."""
        status = await self.get_health_status()
        await self.websocket_manager.send_status_update("current_status", status)