#!/usr/bin/env python3
"""
LiveStream AI Chat Bot - Main Application Entry Point

This is the main entry point for the LiveStream AI Chat Bot backend.
It initializes the FastAPI server, WebSocket connections, and coordinates
all the AI processing components.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from loguru import logger

from core.config import Settings
from core.websocket_manager import WebSocketManager
from core.application import LiveStreamChatBot

# Initialize settings
settings = Settings()

# Initialize FastAPI app
app = FastAPI(
    title="LiveStream AI Chat Bot",
    description="Real-time livestream content analysis and chat response generation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize WebSocket manager
websocket_manager = WebSocketManager()

# Initialize main application
chatbot = LiveStreamChatBot(settings, websocket_manager)


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    logger.info("Starting LiveStream AI Chat Bot backend...")
    await chatbot.initialize()
    logger.info("Backend initialization complete")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down LiveStream AI Chat Bot backend...")
    await chatbot.shutdown()
    logger.info("Backend shutdown complete")


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for frontend communication."""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            await chatbot.handle_websocket_message(data)
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "LiveStream AI Chat Bot Backend", "status": "running"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "components": await chatbot.get_health_status()
    }


if __name__ == "__main__":
    # Configure logging
    logger.add(
        "logs/app.log",
        rotation="1 day",
        retention="30 days",
        level="INFO"
    )

    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )