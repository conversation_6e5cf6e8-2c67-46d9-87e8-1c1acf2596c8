"""
Configuration management for the LiveStream AI Chat Bot.

This module handles all configuration settings including API keys,
model parameters, platform settings, and application behavior.
"""

import os
from typing import Optional, List
from pydantic import BaseSettings, Field
from enum import Enum


class PlatformType(str, Enum):
    """Supported streaming platforms."""
    TWITCH = "twitch"
    YOUTUBE = "youtube"


class AIModel(str, Enum):
    """Supported AI models."""
    GPT_3_5_TURBO = "gpt-3.5-turbo"
    GPT_4 = "gpt-4"
    GPT_4_TURBO = "gpt-4-turbo-preview"


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Server settings
    host: str = Field(default="localhost", env="HOST")
    port: int = Field(default=8000, env="PORT")
    debug: bool = Field(default=True, env="DEBUG")

    # API Keys
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    twitch_client_id: Optional[str] = Field(default=None, env="TWITCH_CLIENT_ID")
    twitch_client_secret: Optional[str] = Field(default=None, env="TWITCH_CLIENT_SECRET")
    twitch_oauth_token: Optional[str] = Field(default=None, env="TWITCH_OAUTH_TOKEN")
    youtube_api_key: Optional[str] = Field(default=None, env="YOUTUBE_API_KEY")

    # AI Model Configuration
    ai_model: AIModel = Field(default=AIModel.GPT_3_5_TURBO, env="AI_MODEL")
    max_tokens: int = Field(default=150, env="MAX_TOKENS")
    temperature: float = Field(default=0.7, env="TEMPERATURE")

    # Platform Settings
    primary_platform: PlatformType = Field(default=PlatformType.TWITCH, env="PRIMARY_PLATFORM")
    target_channel: Optional[str] = Field(default=None, env="TARGET_CHANNEL")

    # Response Behavior
    response_frequency: float = Field(default=0.3, env="RESPONSE_FREQUENCY")  # 30% chance to respond
    min_response_interval: int = Field(default=30, env="MIN_RESPONSE_INTERVAL")  # seconds
    max_response_length: int = Field(default=200, env="MAX_RESPONSE_LENGTH")  # characters

    # Content Analysis
    enable_speech_recognition: bool = Field(default=True, env="ENABLE_SPEECH_RECOGNITION")
    enable_computer_vision: bool = Field(default=True, env="ENABLE_COMPUTER_VISION")
    enable_sentiment_analysis: bool = Field(default=True, env="ENABLE_SENTIMENT_ANALYSIS")

    # Safety Settings
    enable_content_filter: bool = Field(default=True, env="ENABLE_CONTENT_FILTER")
    banned_words: List[str] = Field(default_factory=list, env="BANNED_WORDS")
    safe_mode: bool = Field(default=True, env="SAFE_MODE")

    # Screen Capture Settings
    capture_fps: int = Field(default=2, env="CAPTURE_FPS")  # Low FPS for efficiency
    capture_quality: int = Field(default=50, env="CAPTURE_QUALITY")  # 1-100
    audio_sample_rate: int = Field(default=16000, env="AUDIO_SAMPLE_RATE")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()