{"name": "livestream-ai-chatbot-frontend", "version": "1.0.0", "description": "Desktop frontend for LiveStream AI Chat Bot", "main": "public/electron.js", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-charts": "^6.18.1", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.4", "web-vitals": "^3.5.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.6.4", "react-scripts": "5.0.1", "typescript": "^5.3.2", "wait-on": "^7.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron:dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron:pack": "npm run build && electron-builder", "preelectron:pack": "npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.livestream-ai-chatbot.app", "productName": "LiveStream AI Chat Bot", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}